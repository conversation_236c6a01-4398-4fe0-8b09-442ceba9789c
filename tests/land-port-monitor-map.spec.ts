import { test, expect } from '@playwright/test';

test.describe('Monitor Board - Ports Display', () => {
  test('should toggle land ports visibility on map', async ({ page }) => {
    // 1️⃣ افتح الموقع
    await page.goto('https://tts-test1.adv-photonx.com');

    // 2️⃣ اضغط على "لوحة التحكم" أو "Dashboard"
    const dashboardLink = page.getByRole('button', { name: /لوحة التحكم|Dashboard/i });
    await expect(dashboardLink).toBeVisible();
    await dashboardLink.click();

    // 3️⃣ في الصفحة الجديدة، ابحث عن <ul> واضغط على زر المراقبة أو Monitor
    const monitorButton = page.getByRole('link', { name: /المراقبة|Monitoring/i });
    await expect(monitorButton).toBeVisible();
    await monitorButton.click();

    // 4️⃣ التحقق من الانتقال إلى صفحة الخريطة
    await expect(page).toHaveURL(/.*monitor-board/);

    // 5️⃣ اضغط على زر إعدادات العرض أو Display Settings
    const displaySettingsButton = page.getByRole('tab', { name: /إعدادات العرض|Display Settings/i });
    await expect(displaySettingsButton).toBeVisible();
    await displaySettingsButton.click();

    // 6️⃣ checkbox المنافذ البرية أو Land Ports
    const landPortsCheckbox = page.getByLabel(/المنافذ البرية|Land Ports/i);
    await landPortsCheckbox.check();

    // التحقق من أن الـ checkbox محدد
    await expect(landPortsCheckbox).toBeChecked();

    // 3️⃣ تحقق إن المنافذ ظهرت على الخريطة
    // انتظار قليل للسماح للخريطة بالتحديث
    await page.waitForTimeout(1000);
    
    // التحقق من ظهور المنافذ البرية على الخريطة
    await expect(page.locator('[data-testid="land-ports"]')).toBeVisible();
    // أو يمكن التحقق من وجود عناصر المنافذ البرية
    await expect(page.locator('.land-port-marker')).toHaveCount({
        min: 1,
        toFixed: function (fractionDigits?: number): string {
            throw new Error('Function not implemented.');
        },
        toExponential: function (fractionDigits?: number): string {
            throw new Error('Function not implemented.');
        },
        toPrecision: function (precision?: number): string {
            throw new Error('Function not implemented.');
        }
    });

    // 4️⃣ ألغِ تحديد الـ checkbox
    await landPortsCheckbox.uncheck();
    
    // التحقق من إلغاء التحديد
    await expect(landPortsCheckbox).not.toBeChecked();

    // 5️⃣ تحقق إن المنافذ اختفت من الخريطة
    // انتظار قليل للسماح للخريطة بالتحديث
    await page.waitForTimeout(1000);
    
    // التحقق من اختفاء المنافذ البرية من الخريطة
    await expect(page.locator('[data-testid="land-ports"]')).toBeHidden();
    // أو التحقق من عدم وجود عناصر المنافذ البرية
    await expect(page.locator('.land-port-marker')).toHaveCount(0);

    // // 7️⃣ تحقق من ظهور المنافذ على الخريطة
    // await expect(page.locator('[data-testid="land-ports"]')).toBeVisible();

    // // 8️⃣ ألغِ تحديد checkbox المنافذ البرية
    // await landPortsCheckbox.uncheck();

    // // 9️⃣ تحقق من اختفاء المنافذ من الخريطة
    // await expect(page.locator('[data-testid="land-ports"]')).toBeHidden();
  });

  test('should handle ports checkbox interaction with proper state management', async ({ page }) => {
    await test.step('Navigate to monitor board', async () => {
      await page.goto('/');

      const dashboardLink = page.getByRole('link', { name: /لوحة التحكم|Dashboard/i });
      await expect(dashboardLink).toBeVisible();
      await dashboardLink.click();

      const monitorButton = page.locator('ul').getByRole('button', { name: /المراقبة|Monitor/i });
      await expect(monitorButton).toBeVisible();
      await monitorButton.click();
    });

    await test.step('Open display settings', async () => {
      const displaySettingsButton = page.getByRole('button', { name: /إعدادات العرض|Display Settings/i });
      await expect(displaySettingsButton).toBeVisible();
      await displaySettingsButton.click();

      await expect(page.getByLabel(/المنافذ البرية|Land Ports/i)).toBeVisible();
    });

    await test.step('Toggle ports visibility multiple times', async () => {
      const checkbox = page.getByLabel(/المنافذ البرية|Land Ports/i);
      
      // تحقق من الحالة الأولية
      await expect(checkbox).not.toBeChecked();
      
      // تفعيل المنافذ
      await checkbox.check();
      await expect(checkbox).toBeChecked();
      await expect(page.locator('[data-testid="land-ports"]')).toBeVisible();
      
      // إلغاء تفعيل المنافذ
      await checkbox.uncheck();
      await expect(checkbox).not.toBeChecked();
      await expect(page.locator('[data-testid="land-ports"]')).toBeHidden();
    });
  });
});
