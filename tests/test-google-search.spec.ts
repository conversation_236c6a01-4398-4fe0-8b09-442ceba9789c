// test-google-search.js
const { test, expect } = require('@playwright/test');

test('البحث في جوجل عن Playwright', async ({ page }) => {
  // 1. الانتقال إلى صفحة جوجل
  await page.goto('https://www.google.com');

  // 2. قبول الكوكيز إذا ظهرت النافذة
  try {
    await page.click('button:has-text("قبول الكل")', { timeout: 5000 });
  } catch (error) {
    console.log('نافذة الكوكيز لم تظهر');
  }

  // 3. البحث عن مربع البحث وكتابة كلمة "Playwright"
  await page.fill('textarea[name="q"], input[name="q"]', 'Playwright');

  // 4. النقر على زر البحث أو الضغط على Enter
  await page.keyboard.press('Enter');
  // أو باستخدام النقر على زر البحث إذا كان ظاهراً:
  // await page.click('input[name="btnK"]');

  // 5. الانتظار حتى تحمّل صفحة النتائج
  await page.waitForLoadState('networkidle');

  // 6. التحقق من ظهور نتائج البحث
  await expect(page.locator('#search')).toBeVisible();

  // 7. التحقق من أن أول نتيجة تحتوي على كلمة "Playwright"
  const firstResult = page.locator('#search .g').first();
  await expect(firstResult).toContainText('Playwright', { ignoreCase: true });

  // 8. التقاط صورة للشاشة كدليل
  await page.screenshot({ path: 'search-results.png' });
});