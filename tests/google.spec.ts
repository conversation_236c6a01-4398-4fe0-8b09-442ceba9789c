import { test, expect } from '@playwright/test';

test('Google Search Test', async ({ page }) => {
  // 1) افتح موقع Google
  await page.goto('https://www.google.com');

  // 2) اكتب كلمة في البحث
  await page.locator('textarea[name="q"]').fill('Playwright');

  // 3) اضغط Enter
  await page.keyboard.press('Enter');

  // 4) تأكد إن النتايج ظهرت
  await expect(page).toHaveURL(/search/);
  await expect(page.locator('h3').first()).toBeVisible();

  // 5) خد Screenshot
  await page.screenshot({ path: 'google.png', fullPage: true });
});
