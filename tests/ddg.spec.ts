import { test, expect } from '@playwright/test';

test('DuckDuckGo Search Test', async ({ page }) => {
  // افتح الموقع
  await page.goto('https://duckduckgo.com/');

  // اكتب كلمة البحث واضغط Enter
  await page.locator('input[name="q"]').fill('Playwright');
  await page.keyboard.press('Enter');

  // انتظر ظهور النتائج (اللوجو أو أي عنصر ثابت)
  const firstResult = page.locator('article h2 a').first();
  await firstResult.waitFor({ timeout: 10000 }); // انتظر 10 ثواني بدل 5

  // تحقق من وجود نتيجة ظاهرة
  await expect(firstResult).toBeVisible();

  // خد Screenshot للتأكيد
  await page.screenshot({ path: 'duckduckgo-result.png', fullPage: true });
});
